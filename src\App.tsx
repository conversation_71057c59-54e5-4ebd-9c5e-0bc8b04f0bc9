import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MainLayout from './components/MainLayout';
import ProtectedRoute, { AuthErrorBoundary } from './components/ProtectedRoute';
import DashboardPage from './pages/DashboardPage';
import OrdersPage from './pages/OrdersPage';
import LoginPage from './pages/LoginPage';


function App() {
  return (
    <AuthErrorBoundary>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              <ProtectedRoute requireAuth={false}>
                <LoginPage />
              </ProtectedRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<DashboardPage />} />
            <Route path="orders" element={<OrdersPage />} />
            <Route
              path="menu"
              element={<PlaceholderPage title="Menu Management" />}
            />
            <Route
              path="reservations"
              element={<PlaceholderPage title="Reservations" />}
            />
            <Route
              path="customers"
              element={<PlaceholderPage title="Customer Management" />}
            />
            <Route
              path="settings"
              element={<PlaceholderPage title="Settings" />}
            />
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Router>
    </AuthErrorBoundary>
  );
}

// Placeholder component for pages not yet implemented
const PlaceholderPage: React.FC<{ title: string }> = ({ title }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
      <h3 className="text-xl font-semibold text-slate-900 mb-2">{title}</h3>
      <p className="text-slate-600">
        This page is coming soon in the next phase of development.
      </p>
    </div>
  );
};

// 404 Not Found page
const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-50 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-slate-600 text-2xl">🔍</span>
        </div>
        <h2 className="text-2xl font-semibold text-slate-900 mb-2">
          Page Not Found
        </h2>
        <p className="text-slate-600 mb-4">
          The page you're looking for doesn't exist.
        </p>
        <a
          href="/"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Go to Dashboard
        </a>
      </div>
    </div>
  );
};

export default App;
