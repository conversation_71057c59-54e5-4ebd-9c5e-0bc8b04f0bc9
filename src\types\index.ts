import { Timestamp } from 'firebase/firestore';

// Restaurant data model
export interface Restaurant {
  id: string;
  name: string;
  ownerId: string; // The UID of the user who owns this restaurant
  address: string;
  phone?: string;
  createdAt: Timestamp;
}

// Order data model
export interface Order {
  id: string;
  restaurantId: string;
  customerName: string;
  customerPhone?: string;
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  totalPrice: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'rejected';
  createdAt: Timestamp;
}

// Menu item data model
export interface MenuItem {
  id: string;
  restaurantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  isAvailable: boolean;
  createdAt: Timestamp;
}

// Reservation data model
export interface Reservation {
  id: string;
  restaurantId: string;
  customerName: string;
  customerPhone: string;
  date: string; // ISO date string
  time: string; // Time in HH:MM format
  partySize: number;
  specialRequests?: string;
  status: 'pending' | 'confirmed' | 'declined';
  createdAt: Timestamp;
}

// Loyalty program data model
export interface LoyaltyProgram {
  id: string;
  restaurantId: string;
  purchasesRequired: number; // "Buy X" value
  rewardType: 'free_item';
  rewardValue: number; // Usually 1 for "Get 1 Free"
  isActive: boolean;
  createdAt: Timestamp;
}

// Customer loyalty tracking
export interface CustomerLoyalty {
  id: string;
  customerId: string;
  restaurantId: string;
  currentStamps: number;
  totalRedeemed: number;
  lastPurchase: Timestamp;
}

// Push notification data model
export interface PushNotification {
  id: string;
  restaurantId: string;
  message: string;
  sentAt: Timestamp;
  recipientCount: number;
}

// User authentication data model
export interface User {
  uid: string;
  email: string;
  displayName?: string;
  restaurantIds: string[]; // Array of restaurant IDs the user owns/manages
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Order statistics for dashboard
export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}

// Menu category type
export type MenuCategory =
  | 'Appetizers'
  | 'Main Dishes'
  | 'Desserts'
  | 'Beverages'
  | 'Specials';

// Order status type for filtering
export type OrderStatus = Order['status'];

// Theme types for future dark mode support
export type Theme = 'light' | 'dark';

// Navigation item type for sidebar
export interface NavigationItem {
  name: string;
  path: string;
  icon?: string;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Button variant types
export type ButtonVariant = 'primary' | 'secondary' | 'destructive' | 'ghost';

// Input types
export type InputType = 'text' | 'email' | 'password' | 'number' | 'tel';

// Modal types
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

// Table column definition
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: unknown, item: T) => React.ReactNode;
}

// Pagination types
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  status?: OrderStatus;
  category?: MenuCategory;
  dateFrom?: string;
  dateTo?: string;
}

// Settings types
export interface RestaurantSettings {
  restaurantId: string;
  businessHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    orderAlerts: boolean;
  };
  loyaltyProgram: {
    isEnabled: boolean;
    purchasesRequired: number;
  };
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

// Export utility types
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

export type Required<T> = {
  [P in keyof T]-?: T[P];
};

export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
