import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { NavigationItem } from '../types';
import { useAuth } from '../hooks/useAuth';

const MainLayout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const location = useLocation();
  const { signOut, user, userProfile } = useAuth();

  const navigationItems: NavigationItem[] = [
    { name: 'Dashboard', path: '/' },
    { name: 'Orders', path: '/orders' },
    { name: 'Menu', path: '/menu' },
    { name: 'Reservations', path: '/reservations' },
    { name: 'Customers', path: '/customers' },
    { name: 'Settings', path: '/settings' },
  ];

  const isActivePath = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="min-h-screen bg-slate-50 flex">
      {/* Sidebar */}
      <div
        className={`${
          isSidebarOpen ? 'w-64' : 'w-16'
        } bg-white shadow-lg transition-all duration-300 ease-in-out flex flex-col`}
      >
        {/* Logo/Brand */}
        <div className="p-4 border-b border-slate-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">M</span>
            </div>
            {isSidebarOpen && (
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-slate-900">
                  Mobilify Pro
                </h1>
                <p className="text-xs text-slate-500">Admin Panel</p>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigationItems.map(item => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActivePath(item.path)
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
                  }`}
                >
                  <span
                    className={`w-5 h-5 mr-3 ${!isSidebarOpen && 'mr-0'}`}
                    aria-hidden="true"
                  >
                    {getIconForPath(item.path)}
                  </span>
                  {isSidebarOpen && <span>{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Toggle Button */}
        <div className="p-4 border-t border-slate-200">
          <button
            onClick={toggleSidebar}
            className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-slate-600 hover:bg-slate-50 hover:text-slate-900 rounded-lg transition-colors"
          >
            {isSidebarOpen ? (
              <>
                <span className="w-5 h-5 mr-3">←</span>
                <span>Collapse</span>
              </>
            ) : (
              <span className="w-5 h-5">→</span>
            )}
          </button>
        </div>

        {/* User Section */}
        <div className="p-4 border-t border-slate-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-slate-300 rounded-full flex items-center justify-center">
              <span className="text-slate-600 font-medium text-sm">
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
            {isSidebarOpen && (
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-slate-900">
                  {user?.email || 'Restaurant Owner'}
                </p>
                <button
                  onClick={handleSignOut}
                  className="text-xs text-slate-500 hover:text-slate-700 transition-colors"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-slate-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-slate-900">
                {getPageTitle(location.pathname)}
              </h2>
              <p className="text-sm text-slate-500">
                {getPageDescription(location.pathname)}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {/* Notification Bell */}
              <button className="p-2 text-slate-400 hover:text-slate-600 transition-colors">
                <span className="w-5 h-5 block">🔔</span>
              </button>
              {/* Profile */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-sm">
                    {userProfile?.restaurantIds?.[0]?.charAt(0).toUpperCase() || 'R'}
                  </span>
                </div>
                <span className="text-sm font-medium text-slate-700">
                  {userProfile?.displayName || 'Restaurant'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6 overflow-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

// Helper function to get icon for navigation items
const getIconForPath = (path: string): string => {
  switch (path) {
    case '/':
      return '📊';
    case '/orders':
      return '📋';
    case '/menu':
      return '🍽️';
    case '/reservations':
      return '📅';
    case '/customers':
      return '👥';
    case '/settings':
      return '⚙️';
    default:
      return '📄';
  }
};

// Helper function to get page title
const getPageTitle = (pathname: string): string => {
  switch (pathname) {
    case '/':
      return 'Dashboard';
    case '/orders':
      return 'Live Orders';
    case '/menu':
      return 'Menu Management';
    case '/reservations':
      return 'Reservations';
    case '/customers':
      return 'Customer Management';
    case '/settings':
      return 'Settings';
    default:
      return 'Mobilify Pro';
  }
};

// Helper function to get page description
const getPageDescription = (pathname: string): string => {
  switch (pathname) {
    case '/':
      return 'Overview of your restaurant performance';
    case '/orders':
      return 'Manage incoming orders in real-time';
    case '/menu':
      return 'Update your menu items and availability';
    case '/reservations':
      return 'View and manage table reservations';
    case '/customers':
      return 'Customer information and loyalty program';
    case '/settings':
      return 'Restaurant settings and preferences';
    default:
      return 'Restaurant management system';
  }
};

export default MainLayout;
