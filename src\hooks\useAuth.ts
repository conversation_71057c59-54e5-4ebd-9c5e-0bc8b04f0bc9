import { useState, useEffect, useCallback } from 'react';
import { User } from 'firebase/auth';
import AuthService from '../services/authService';
import { User as AppUser, LoadingState } from '../types';

// Authentication state interface
interface AuthState {
  user: User | null;
  userProfile: AppUser | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
}

// Authentication actions interface
interface AuthActions {
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
  refreshProfile: () => Promise<void>;
}

// Combined hook return type
type UseAuthReturn = AuthState & AuthActions;

// Custom hook for authentication management
export const useAuth = (): UseAuthReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    userProfile: null,
    isLoading: true,
    error: null,
    isAuthenticated: false,
  });

  // Load user profile data
  const loadUserProfile = useCallback(async (user: User | null) => {
    if (!user) {
      setAuthState(prev => ({
        ...prev,
        userProfile: null,
        isAuthenticated: false,
        isLoading: false,
      }));
      return;
    }

    try {
      const userProfile = await AuthService.getUserProfile(user.uid);
      setAuthState(prev => ({
        ...prev,
        user,
        userProfile,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Error loading user profile:', error);
      setAuthState(prev => ({
        ...prev,
        user,
        userProfile: null,
        isAuthenticated: true,
        isLoading: false,
        error: 'Failed to load user profile',
      }));
    }
  }, []);

  // Sign in function
  const signIn = useCallback(async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Validate input
      if (!AuthService.isValidEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      if (!AuthService.isValidPassword(password)) {
        throw new Error('Password must be at least 6 characters long');
      }

      // Attempt sign in
      const user = await AuthService.signIn(email, password);
      
      // Load user profile will be handled by the auth state listener
      // No need to manually update state here
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  // Sign out function
  const signOut = useCallback(async () => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await AuthService.signOut();
      // State will be updated by the auth state listener
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh user profile
  const refreshProfile = useCallback(async () => {
    if (authState.user) {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      await loadUserProfile(authState.user);
    }
  }, [authState.user, loadUserProfile]);

  // Set up authentication state listener
  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChanged((user) => {
      loadUserProfile(user);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [loadUserProfile]);

  return {
    // State
    user: authState.user,
    userProfile: authState.userProfile,
    isLoading: authState.isLoading,
    error: authState.error,
    isAuthenticated: authState.isAuthenticated,
    
    // Actions
    signIn,
    signOut,
    clearError,
    refreshProfile,
  };
};

// Hook for checking if user owns a specific restaurant
export const useRestaurantOwnership = (restaurantId: string | null) => {
  const { user, userProfile } = useAuth();
  const [isOwner, setIsOwner] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkOwnership = async () => {
      if (!user || !restaurantId) {
        setIsOwner(false);
        setIsLoading(false);
        return;
      }

      try {
        // First check from cached user profile
        if (userProfile?.restaurantIds?.includes(restaurantId)) {
          setIsOwner(true);
          setIsLoading(false);
          return;
        }

        // If not in cache, check from database
        const ownsRestaurant = await AuthService.userOwnsRestaurant(
          user.uid,
          restaurantId
        );
        setIsOwner(ownsRestaurant);
      } catch (error) {
        console.error('Error checking restaurant ownership:', error);
        setIsOwner(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkOwnership();
  }, [user, userProfile, restaurantId]);

  return { isOwner, isLoading };
};

// Hook for getting user's restaurants
export const useUserRestaurants = () => {
  const { user, userProfile } = useAuth();
  const [restaurants, setRestaurants] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchRestaurants = async () => {
      if (!user) {
        setRestaurants([]);
        setIsLoading(false);
        return;
      }

      try {
        // First try from cached user profile
        if (userProfile?.restaurantIds) {
          setRestaurants(userProfile.restaurantIds);
          setIsLoading(false);
          return;
        }

        // If not in cache, fetch from database
        const userRestaurants = await AuthService.getUserRestaurants(user.uid);
        setRestaurants(userRestaurants);
      } catch (error) {
        console.error('Error fetching user restaurants:', error);
        setRestaurants([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRestaurants();
  }, [user, userProfile]);

  return { restaurants, isLoading };
};

export default useAuth;
